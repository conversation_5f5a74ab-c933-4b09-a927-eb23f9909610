<template>
  <div
    class="floating-button"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
  >
    <div class="icon-content" @mousedown="handleMouseDown" @click="handleClick">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C7.4,22 3.55,18.92 2.36,14.73L6.19,16.31C6.45,17.6 7.6,18.58 9,18.58C10.68,18.58 12.06,17.21 12.06,15.53C12.06,13.85 10.68,12.47 9,12.47C8.67,12.47 8.35,12.54 8.06,12.66L6.5,10.47C8.41,8.85 11.04,8.12 13.68,8.68C16.32,9.24 18.33,11.5 18.33,14.17C18.33,17.5 15.5,20.33 12.17,20.33C9.5,20.33 7.24,18.32 6.68,15.68L2.36,14.73C3.55,18.92 7.4,22 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
      </svg>
      <div class="pulse-ring"></div>
    </div>
    <div class="tooltip">AI 助手</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const position = ref({
  x: window.innerWidth - 84,
  y: window.innerHeight - 84
});
let dragging = false;
let dragStarted = false;
let offset = { x: 0, y: 0 };

function handleMouseDown(e: MouseEvent) {
  dragging = true;
  dragStarted = false;
  offset.x = e.clientX - position.value.x;
  offset.y = e.clientY - position.value.y;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
}

function onDrag(e: MouseEvent) {
  if (!dragging) return;
  dragStarted = true;
  position.value.x = Math.max(0, Math.min(window.innerWidth - 60, e.clientX - offset.x));
  position.value.y = Math.max(0, Math.min(window.innerHeight - 60, e.clientY - offset.y));
}

function stopDrag() {
  dragging = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

function handleClick(e: MouseEvent) {
  console.log('handleClick called, dragStarted:', dragStarted);
  // 只有在没有拖拽的情况下才处理点击
  if (!dragStarted) {
    console.log('Calling openSidebar');
    openSidebar();
  }
  dragStarted = false;
}
function openSidebar() {
  console.log('openSidebar called');
  console.log('window.electronAPI:', window.electronAPI);

  if (window.electronAPI && window.electronAPI.openSidebar) {
    console.log('Calling electronAPI.openSidebar');
    window.electronAPI.openSidebar();
  } else {
    console.error('electronAPI.openSidebar not available');
    console.log('Available methods:', Object.keys(window.electronAPI || {}));
  }
}
onMounted(() => {
  // 保证窗口大小变化时按钮不出界
  window.addEventListener('resize', () => {
    position.value.x = Math.min(position.value.x, window.innerWidth - 60);
    position.value.y = Math.min(position.value.y, window.innerHeight - 60);
  });
});
</script>

<style scoped>
.floating-button {
  position: fixed;
  width: 60px;
  height: 60px;
  left: 0;
  top: 0;
  z-index: 10000;
  cursor: move;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-content {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0;
  }
}
.tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}
.floating-button:hover .tooltip {
  opacity: 1;
}
</style> 